"smart_sources:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md": {"path":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09240332,0.00458976,-0.02247869,-0.05152578,0.00445898,-0.00697071,-0.0938212,0.0205155,0.03599713,-0.02667364,0.0014883,-0.03543838,0.03838866,0.03241917,0.04098187,0.0015188,-0.02672837,0.0264016,-0.05809403,-0.00514826,0.05298212,-0.06172568,-0.00632244,-0.06579359,-0.03152711,0.00440181,0.01505126,0.00306166,-0.02276057,-0.14192376,-0.01183697,-0.00603676,0.03496629,0.04766882,0.00551694,-0.03490269,0.05548946,0.02674743,-0.03076798,0.06527641,0.02275646,0.02705793,0.02823325,0.01620215,-0.01485765,-0.05940243,-0.03469278,-0.01625865,-0.0096408,-0.04176534,-0.04533107,-0.02299082,-0.06641849,0.00918382,-0.04836519,-0.01375836,0.01097739,0.03317147,0.07321395,0.00803538,0.03389953,0.00448597,-0.2243311,0.06253518,0.01372885,-0.00351891,-0.00653356,-0.02587863,0.01643365,0.0428003,-0.07203456,0.01870638,-0.00297685,0.05529483,0.055558,-0.04336405,-0.00464287,-0.03176956,-0.01065439,-0.01583358,-0.02538824,0.06093604,-0.00299086,-0.00403516,-0.06724742,0.01202441,-0.01152785,0.00486913,0.03039636,0.01602879,-0.02870509,-0.08377086,0.01278191,0.00080627,0.01411901,0.00375017,0.01450977,0.04060179,-0.12630855,0.10656565,-0.0761456,-0.03042069,0.02399844,-0.08146722,0.00370798,-0.00023885,-0.04910437,-0.01347953,-0.06561555,-0.00884881,-0.05608701,-0.0216244,0.04375289,-0.00749714,0.02471993,-0.00382345,0.0159867,-0.05056159,-0.01552702,0.01966506,-0.00224879,-0.01843433,0.10127432,-0.01955312,-0.0034691,-0.02309191,0.05691743,0.05577616,0.02289686,0.04982287,0.02885943,-0.05104003,-0.03131934,0.01937684,-0.00486769,0.03200188,-0.01494628,0.00801925,-0.00534504,-0.04400513,-0.01278988,-0.03815639,0.00781851,-0.09293886,-0.05617816,0.08890761,-0.03607368,0.01795015,0.04587108,-0.01787828,0.01933938,0.05310835,-0.0303974,-0.01660527,-0.02652096,0.00735306,0.10509258,0.14984198,-0.04176509,-0.01613785,-0.00722304,0.01110627,-0.08206873,0.16564219,0.02536777,-0.04303335,0.00488029,-0.00164353,0.05410931,-0.00749337,-0.00017723,0.01818693,0.00791247,0.03641925,0.08136434,-0.01863485,-0.04033136,0.00908048,0.06004705,-0.00178825,0.05601823,-0.00246019,-0.08055535,0.05338768,0.04217538,-0.08977581,-0.01805172,-0.05577399,-0.03354004,-0.0353545,-0.11482967,0.00234531,0.01224708,-0.01835058,0.00160403,-0.05257268,0.00333782,0.01179675,0.01868453,-0.05601712,0.08191808,0.01902705,-0.07564177,-0.05405911,-0.0374721,-0.04131735,0.03898632,-0.01815071,0.04285515,0.01976192,0.00880527,0.03177872,0.00884161,0.04222932,-0.03404275,0.00772762,-0.01651175,0.03426909,0.02031572,0.03950236,-0.00132247,-0.02186014,-0.10092136,-0.2261008,-0.04403357,0.05240598,-0.03385948,-0.01381085,0.01362177,0.05610215,-0.00356185,0.07408129,0.06565888,0.09579334,0.05337505,-0.04703755,-0.02214439,0.03271421,0.01375484,0.0154198,-0.00859263,-0.02218107,0.00446171,0.00041504,0.04040802,-0.00611796,-0.02346232,0.0292137,-0.04231209,0.09553088,0.02599053,0.04980443,0.02688281,0.01380708,0.0085657,0.02637839,-0.11414761,-0.01146156,0.08372638,-0.01623012,-0.03730303,0.00277814,-0.02334019,0.00838722,0.01355811,-0.04007968,-0.10590015,-0.0044751,-0.01403423,-0.00621866,-0.00814834,-0.00723119,0.0539225,-0.0059302,0.02198967,0.0086196,0.01190615,-0.01190378,-0.02673336,-0.02200586,-0.06756084,0.00133489,0.00446465,0.02269553,-0.04890326,0.03776081,0.02904492,0.01082146,-0.0128974,0.01772327,-0.02086653,0.00257404,-0.03341622,-0.05659755,0.12294399,0.01328162,-0.03845797,0.0263415,0.072578,-0.00568884,-0.0403224,0.01687637,-0.00575617,0.04602629,0.0088264,0.04864966,0.02784662,0.01540327,-0.00100142,0.00556475,-0.01608187,0.07553512,-0.05470462,-0.08677986,-0.01987741,-0.05050274,0.01301007,0.07828639,-0.04339487,-0.30095667,0.02589241,0.03250124,0.00205883,0.02356553,0.02200857,0.03416838,-0.00228363,-0.06517625,-0.01554988,-0.04223644,0.06114418,-0.02730159,-0.05394506,-0.02848856,-0.06908835,0.07140856,-0.00646604,0.09535725,0.00602506,-0.0047828,0.0543165,0.20094934,0.03206688,0.1018948,0.00324648,0.02943982,0.05795858,0.07847351,0.04691496,0.03513338,-0.04149945,0.00136334,-0.03508909,0.03147869,0.05454618,-0.03796773,0.02309535,0.05760122,0.01853246,-0.01733425,0.09007174,-0.05296475,0.01930355,0.09958115,0.00371509,0.02588625,-0.06472743,-0.00856536,0.05478005,0.04597998,0.03505578,-0.01109206,-0.03270254,0.01511024,0.01440096,0.0219664,-0.08153225,-0.06377564,0.00064436,0.02397629,-0.02984175,0.1075004,0.10199356,0.07875502],"last_embed":{"hash":"fca7f5c10e74d5bcffabfc8f26169f0c79c50080590514b80a148474696a54eb","tokens":490}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10496572,-0.02066137,0.05934949,0.04977274,-0.01987233,0.0772365,0.02215941,-0.02836599,-0.01377349,-0.02863212,0.01040756,0.03050469,0.01937152,-0.09539112,0.10332146,-0.00562773,-0.01761195,0.0106635,0.00248623,-0.05161548,0.00203152,-0.04152403,0.00079489,0.03220595,0.03694351,0.0236298,-0.03089771,-0.03222657,0.0241057,0.01368569,0.04911136,0.02926207,0.04099762,-0.00369493,0.06070412,0.04433398,0.01700189,0.00849038,0.0883826,0.00204878,0.01748373,0.01111981,-0.02472536,0.01746234,-0.04602792,0.02857146,-0.01435644,-0.00345997,0.03089339,-0.00639832,0.01388295,-0.02270246,0.0318776,-0.00263253,0.0353281,-0.01605034,-0.06491799,-0.05375083,-0.04622452,-0.02853777,-0.0170086,-0.04438076,0.01570659,-0.00752404,-0.00253529,-0.01143588,-0.02240001,-0.05528729,0.02167482,0.00972732,0.00559034,-0.0043482,0.06308943,-0.01492543,0.00633141,0.053282,0.01943132,-0.00720902,0.00262231,0.00725554,-0.05454193,0.00058952,0.02805646,0.06372616,0.03132394,-0.0351571,0.06325655,-0.076644,-0.0406444,0.01519199,-0.02522198,0.01550179,-0.08591011,-0.05370917,0.07965954,0.05172896,-0.00284226,-0.02530103,0.01201456,-0.00975082,-0.01419142,0.00344938,-0.06914306,0.01012991,-0.02772915,-0.03086641,-0.03215047,0.06433905,-0.01533077,-0.00724704,0.01777129,0.01339464,-0.05849536,0.00354619,-0.0422245,0.00522707,-0.01336731,-0.00228154,-0.01945768,0.0115089,0.02084218,0.06932221,-0.01820254,-0.01710739,-0.01945356,-0.06794498,0.02981875,0.00281133,0.06069339,-0.07179137,0.00014355,0.06186776,0.01353812,0.01809549,0.04800758,0.04285548,0.05416639,-0.02100816,-0.03256463,0.0030919,0.02453728,0.01692148,0.01870786,0.00530371,-0.02168719,0.02192122,-0.01982673,0.00640534,-0.00414886,0.01935392,0.03092002,0.00063593,-0.01716658,-0.03481648,-0.00200003,-0.02635106,0.07453582,-0.01539436,-0.00598687,-0.05275212,-0.02034599,0.02664305,-0.08170468,-0.0050437,0.00982642,-0.02924742,-0.05627217,-0.00134327,-0.00104929,0.01701034,0.03349678,0.08227248,0.03539107,-0.07724418,0.01588677,-0.02422678,-0.00415001,-0.04102933,-0.01508306,0.05087901,0.0595668,0.03892117,0.05628363,-0.02602232,-0.0286406,0.04358662,-0.0267318,-0.00301451,-0.03134239,0.01042758,0.02014549,0.05205454,0.03524471,0.00140912,0.02810519,-0.0002544,0.01260759,-0.06344862,0.02215654,0.04356837,-0.05715807,0.01529186,-0.03605839,-0.08947194,0.07293598,-0.00821353,0.01725285,-0.02430146,-0.01991128,0.01077427,-0.02962421,0.00368512,-0.00019342,-0.04448483,0.00988636,-0.01177122,0.00599442,-0.01301745,0.046706,-0.05817311,0.01823871,0.0035171,-0.0601762,0.06110733,-0.0283204,-0.02474942,-0.00579022,-0.01597843,0.03014684,-0.01079357,0.00075478,0.05532585,0.01735909,-0.00357538,-0.02475688,-0.01455557,0.05190744,-0.0077244,0.05651672,-0.04406324,-0.04415193,0.02884063,0.06375972,0.00165008,0.00689394,-0.00509753,-0.02863914,0.03754289,0.00189762,0.02549013,-0.02118803,0.00402867,0.00496682,0.02781753,0.0000818,-0.03454747,-0.04912271,0.0668181,0.07861135,-0.05227171,0.02845896,-0.02465617,-0.06651055,0.06523962,0.04072867,-0.02491705,-0.06519892,0.01533839,0.03584741,-0.00137832,0.03286041,-0.02532014,0.01090976,0.00854503,-0.04043061,-0.02493682,0.02137467,-0.02666681,0.05668025,-0.01839626,0.01481474,-0.04599717,-0.0394941,-0.02909032,-0.0156367,0.06952932,0.04003756,-0.03370382,0.0464458,0.04065173,-0.00943822,-0.02714481,-0.04716339,-0.01520898,0.00288433,-0.03532709,-0.01739459,-0.00309704,-0.00767216,-0.07329736,0.02124492,0.04605426,-0.0109522,-0.02177748,0.02645062,-0.00404018,0.00322007,0.00578207,0.01602746,-0.04686591,0.00339521,-0.02128115,0.00702348,-0.06946515,-0.03163793,0.06353776,0.00219353,0.00305932,-0.02593419,-0.01745653,-0.0184139,0.04308258,-0.03401415,0.02715341,-0.01505701,-0.01766489,-0.01843906,0.04749748,0.08379577,0.02302246,-0.05288389,-0.00586256,-0.04653071,-0.05652462,0.01519198,0.05111078,-0.0075772,-0.04084722,0.02657144,0.0483196,-0.06894895,0.01839331,-0.05238624,-0.01958613,-0.0132365,0.04641618,0.0214591,0.05570374,-0.05371046,-0.02895966,-0.00630205,0.03131196,-0.045243,-0.00394669,-0.02689949,0.04111996,-0.03013909,-0.01945041,-0.07858392,-0.00376202,-0.01652536,-0.03526252,0.04691121,-0.00621678,0.04545971,0.01615055,0.02953035,0.07650119,-0.04947619,-0.05132233,-0.02206886,-0.03621517,-0.00896038,0.08102067,0.01411777,-0.00693976,0.01908249,-0.03066295,0.04848253,-0.05423971,-0.04583855,-0.04697224,0.04969621,0.02263989,-0.02637007,-0.00877637,-0.00663877,-0.00376415,0.06578947,-0.0983872,0.02758868,-0.00359088,-0.04448331,-0.01109419,0.05228483,0.01571533,-0.03873157,-0.06097686,0.01015303,-0.03707138,0.02821215,-0.00931677,-0.0295437,-0.0239706,-0.02824238,0.00234667,-0.08757401,0.0501574,-0.01354085,-0.02361514,-0.00719777,-0.01930496,0.03703124,0.02422538,-0.05322707,0.01774735,0.08938392,-0.08795127,-0.00718056,-0.02480535,0.04026065,0.00531067,0.02394586,0.03573608,-0.10222045,0.01382626,-0.05169656,0.00444306,0.06222492,0.03081536,-0.01497886,-0.05900036,-0.02964646,-0.00088773,0.01555443,0.00872423,0.01546674,-0.03871937,0.00119597,-0.02221923,-0.02905355,0.02048803,-0.00255399,-0.01220372,-0.02633468,0.0053557,-0.01466391,-0.07035527,0.0094765,0.0269835,-0.00401224,-0.02113947,-0.04546228,0.01727422,0.04154503,0.02111888,0.01016714,0.00987483,-0.00109402,0.03531663,-0.05710334,0.00667117,-0.02188813,-0.0235876,-0.00515216,0.03265271,0.0776436,0.01761581,-0.05078962,-0.04128955,0.01481148,-0.04387242,0.0378763,-0.05083915,0.02501355,0.01803433,0.01978362,0.05643205,-0.03722803,0.02609421,0.01909118,0.01434485,-0.03457493,-0.05887034,-0.03277026,0.03530127,-0.02399984,0.01819987,0.0306702,-0.03895126,0.03742723,0.02729187,-0.00360099,0.01843842,-0.06353737,-0.00100399,-0.03486916,0.00630571,0.00029902,0.05028588,-0.00978404,-0.02317633,0.02619387,0.04000542,0.00216108,0.00137877,-0.0377407,0.01560531,0.04210385,-0.04037602,-0.0013878,-0.03357871,0.01597529,0.00959367,-0.02048156,-0.01730656,0.04245698,-0.00281185,0.01009955,0.01570402,0.06808917,0.04357297,-0.04251045,-0.03841501,0.01746732,-0.00313834,-0.05413682,0.03767386,0.0211715,-0.01443793,0.02701142,-0.0005443,-0.04183965,-0.05787672,0.0148967,-0.02148768,0.08001653,0.00322903,-0.03696674,0.00486865,-0.01474479,0.06334776,-0.04952228,-0.01854488,-0.02030732,0.04596801,0.00060601,-0.0127768,0.00575181,0.01610729,0.00116329,0.05966105,0.0193881,0.0004517,0.00515218,-0.00878646,0.0263166,-0.06281226,-0.02799941,0.04507707,-0.00775227,0.07519586,-0.00440132,-0.03642258,0.00973671,-0.04541795,-0.04815072,-0.06522816,0.05170797,0.0028567,-0.06176409,-0.00873966,0.03163698,-0.02301374,-0.00938204,-0.03508904,-0.03219802,0.03560122,0.05292143,-0.02421558,0.00607048,-0.02219515,0.09218638,-0.0926562,0.01094416,0.02425985,0.00494745,-0.01400219,0.01403085,0.00711077,0.0340094,-0.05365979,-0.0241142,-0.01239624,-0.01294909,-0.04323275,-0.04670287,0.03175749,0.05646465,-0.02552311,-0.00198672,0.03391137,0.02642089,-0.01029344,-0.00825435,-0.0358071,-0.0045465,-0.03749398,0.01155788,0.00905575,-0.02681921,0.09238694,0.00515607,-0.05441929,0.016498,-0.03147708,-0.02228137,0.00152142,0.00779269,0.07794633,-0.06175473,-0.05723092,0.01950557,0.01442254,0.01195859,0.0119742,0.00751002,0.02039993,-0.00263017,0.05093424,-0.03225402,-0.01263191,0.0174729,0.02586294,0.02957676,0.0160297,-0.06017242,-0.01384348,0.00581517,0.00003068,0.04158584,-0.04619075,-0.01400293,-0.00153715,-0.01323809,-0.05533936,-0.04442759,-0.00947414,-0.0222992,0.03193519,0.04774363,-0.01068205,0.04874894,-0.02811496,0.01511773,-0.03903564,-0.04141735,0.01881322,-0.02010044,-0.05807641,-0.00753771,-0.02109166,-0.05493296,0.03443756,0.00101596,-0.05585075,0.0188882,0.0277203,-0.05147395,-0.03581873,0.02097586,-0.02004953,0.01320087,-0.05807294,-0.00025835,0.03109771,-0.01092883,0.03716201,0.00810985,-0.05433587,0.02198845,-0.03188574,0.04000724,-0.00678858,0.0299458,-0.01132746,0.00363811,-0.00649973,0.02765404,-0.0319679,-0.0281633,-0.01836527,0.00045743,-0.00890408,-0.01953198,-0.05734326,0.0159691,0.03980236,-0.0047079,-0.04212402,-0.01297096,0.05659859,-0.02210218,0.08183309,0.02524741,0.01372593,0.00869331,-0.01421521,0.04080908,0.06150616,-0.04156467,0.01742033,0.0194656,-0.04207899,0.03439359,0.01780761,-0.00215771,0.02216254,-0.05191192,0.05156195,0.0261075,0.05149192,0.03276099,0.02086522,-0.04009282,-0.00925324,-0.07952648,-0.01494034,0.00736644,0.04138276,0.01142258,-0.03687688,-0.03737206,0.03795716,-0.00854348,0.00994324,0.00772284,-0.01732305,0.0222351,-0.00573762,0.02168067,-0.05746521,-0.02029759,-0.04312952,-0.02143747,0.02635533,0.01902317,-0.00117858,-0.00270765,0.04950377,-0.03518573,0.01324699,-0.04133532,0.0788446,0.05948631,-0.00087292,0.04006381,-0.05473921,8.8e-7,-0.0167363,-0.02001043,0.00776506,0.00620368,-0.0329702,-0.03560871,0.0139212,0.03562467,0.02816838],"last_embed":{"tokens":1235,"hash":"qk22tt"}}},"last_read":{"hash":"qk22tt","at":1750819201950},"class_name":"SmartSource","outlinks":[{"title":"基于WSL的隧道转发","target":"基于WSL的隧道转发","line":5},{"title":"bash shell","target":"bash shell","line":11},{"title":"Linux","target":"Linux","line":11},{"title":"Linux","target":"Linux","line":11},{"title":"Ruby","target":"Ruby","line":11},{"title":"windows系统","target":"windows系统","line":11},{"title":"内核包","target":"https://learn.microsoft.com/zh-cn/windows/wsl/install-manual#step-4---download-the-linuxkernel-update-package ","line":13},{"title":"windows系统","target":"windows系统","line":18},{"title":"windows系统","target":"windows系统","line":25},{"title":"Linux","target":"Linux","line":34},{"title":"windows系统","target":"windows系统","line":43},{"title":"Linux","target":"Linux","line":44},{"title":"Kali Linux","target":"Kali Linux","line":100},{"title":"NAT","target":"NAT","line":112},{"title":"Pasted image 20240705132241.png","target":"Pasted image 20240705132241.png","line":124},{"title":"#修改WSL配置文件","target":"#修改WSL配置文件","line":140}],"metadata":{"tags":["操作系统/windows/子系统"],"进阶知识":["[[基于WSL的隧道转发]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,22],"#简介#基于Windows的子系统(Linux系统)":[10,22],"#简介#基于Windows的子系统(Linux系统)#{1}":[11,22],"#安装wsl":[23,110],"#安装wsl#安装WSL":[24,26],"#安装wsl#安装WSL#{1}":[25,26],"#安装wsl#开启WSL的安全权限":[27,35],"#安装wsl#开启WSL的安全权限#{1}":[28,30],"#安装wsl#开启WSL的安全权限#{2}":[31,32],"#安装wsl#开启WSL的安全权限#{3}":[33,34],"#安装wsl#开启WSL的安全权限#{4}":[35,35],"#安装wsl#开启DISM":[36,54],"#安装wsl#开启DISM#{1}":[37,41],"#安装wsl#开启DISM#{2}":[42,44],"#安装wsl#开启DISM#{3}":[45,48],"#安装wsl#开启DISM#{4}":[49,52],"#安装wsl#开启DISM#{5}":[50,52],"#安装wsl#开启DISM#{6}":[53,53],"#安装wsl#开启DISM#{7}":[54,54],"#安装wsl#安装Kali":[55,65],"#安装wsl#安装Kali#{1}":[57,59],"#安装wsl#安装Kali#{2}":[60,65],"#安装wsl#安装Kali#{3}":[61,65],"#安装wsl#子系统迁移":[66,99],"#安装wsl#子系统迁移#{1}":[67,72],"#安装wsl#子系统迁移#{2}":[73,73],"#安装wsl#子系统迁移#{3}":[74,77],"#安装wsl#子系统迁移#{4}":[78,78],"#安装wsl#子系统迁移#{5}":[79,82],"#安装wsl#子系统迁移#{6}":[83,83],"#安装wsl#子系统迁移#{7}":[84,87],"#安装wsl#子系统迁移#{8}":[88,88],"#安装wsl#子系统迁移#{9}":[89,92],"#安装wsl#子系统迁移#{10}":[93,93],"#安装wsl#子系统迁移#{11}":[94,97],"#安装wsl#子系统迁移#{12}":[95,97],"#安装wsl#子系统迁移#{13}":[98,98],"#安装wsl#子系统迁移#{14}":[99,99],"#安装wsl#使用[[Kali Linux]]的GUI界面":[100,110],"#安装wsl#使用[[Kali Linux]]的GUI界面#{1}":[101,103],"#安装wsl#使用[[Kali Linux]]的GUI界面#{2}":[104,107],"#安装wsl#使用[[Kali Linux]]的GUI界面#{3}":[105,107],"#安装wsl#使用[[Kali Linux]]的GUI界面#{4}":[108,109],"#安装wsl#使用[[Kali Linux]]的GUI界面#{5}":[110,110],"#修改WSL的网络配置":[111,128],"#修改WSL的网络配置#{1}":[112,115],"#修改WSL的网络配置#{2}":[116,119],"#修改WSL的网络配置#{3}":[117,119],"#修改WSL的网络配置#{4}":[120,120],"#修改WSL的网络配置#{5}":[121,121],"#修改WSL的网络配置#{6}":[122,122],"#修改WSL的网络配置#{7}":[123,123],"#修改WSL的网络配置#{8}":[124,124],"#修改WSL的网络配置#{9}":[125,126],"#修改WSL的网络配置#{10}":[127,128],"#开机自启动":[129,143],"#开机自启动#{1}":[130,130],"#开机自启动#{2}":[131,131],"#开机自启动#{3}":[132,136],"#开机自启动#{4}":[133,136],"#开机自启动#{5}":[137,137],"#开机自启动#{6}":[138,143],"#修改WSL配置文件":[144,147]},"last_import":{"mtime":1740737681858,"size":4538,"at":1748488128956,"hash":"qk22tt"},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md","last_embed":{"hash":"qk22tt","at":1750819201950}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07689092,-0.02075746,0.0363138,0.04009637,-0.02565793,0.06814766,0.03266236,-0.00230435,-0.02316673,-0.03485994,0.00814913,0.04772414,0.00512981,-0.05536847,0.0652735,0.01607635,-0.04404554,0.00100843,-0.03626351,-0.07106844,0.00723425,-0.04787698,0.01461408,0.02073299,0.02974049,0.06141223,-0.02031439,-0.00837683,-0.00802453,0.02372865,0.06357806,0.0339202,0.01745787,0.01553393,0.00352886,0.02482561,0.02215116,0.00749039,0.05749862,0.01607748,0.01326696,0.01038302,-0.04563257,0.0042795,-0.05247112,0.05102981,-0.01616167,-0.00697397,0.01732697,0.00916476,-0.00888237,-0.0348778,0.03433357,0.02385507,0.02866977,-0.01430888,-0.05314912,-0.02558865,-0.02909096,-0.02773131,-0.00997824,-0.02266569,0.00660669,-0.01179151,0.01549271,0.01993911,-0.03027655,-0.05955418,-0.0004313,-0.00038216,0.00870226,-0.00070287,0.04337315,-0.01419521,0.00441827,0.05224667,0.02696071,-0.02026404,-0.01128349,-0.00415176,-0.04232512,-0.02048073,-0.00406722,0.09194376,0.02118654,-0.05332052,0.07144739,-0.07021402,-0.03053217,0.01254123,-0.02142117,0.03354366,-0.11250383,-0.03750336,0.04306074,0.07730678,0.03563156,-0.03509043,-0.01203831,-0.00749103,-0.04817185,0.00711377,-0.05706109,0.01799508,-0.02496231,-0.03789522,-0.05573906,0.05866603,-0.03038605,0.00325808,0.03475117,0.02088074,-0.04434126,-0.0102741,-0.04978035,0.00511574,-0.01669882,0.03071302,0.01862141,-0.00725178,0.03619618,0.05006098,-0.00879302,-0.03365682,-0.05197502,-0.05235389,0.04527076,-0.0452265,0.01028427,-0.07244585,-0.01263016,0.03452032,0.02935871,0.02758035,0.0288199,0.02474643,0.04469696,-0.02723462,-0.03221941,0.01995308,0.03001957,0.02068035,0.00833485,-0.02468495,0.01073755,0.03165022,-0.00910152,-0.03135294,-0.01364884,0.0351532,-0.00051404,0.01188116,-0.01873719,-0.03968561,-0.03600335,-0.03799627,0.09586044,-0.01074087,-0.0191282,-0.04071284,-0.04428438,0.03712577,-0.08104178,-0.00174922,0.01643651,-0.02276368,-0.03887688,-0.02298088,0.01228818,0.00303702,0.00775506,0.07520454,0.03588845,-0.080246,0.01484007,-0.01279499,0.00498389,-0.04383115,0.01956003,0.03966167,0.06587894,0.01418916,0.05508293,0.00432853,-0.04405741,0.06622895,-0.05616709,0.01136321,-0.01578206,0.02200814,-0.00028719,0.02307429,0.01918695,-0.02008573,0.06180202,0.00440912,-0.01072485,-0.05308163,0.01553986,0.03835059,-0.05877948,-0.006089,-0.03685181,-0.06624676,0.05430157,0.01416692,0.02553789,-0.0172249,0.00766472,0.03739053,-0.0075066,0.01433377,0.0071644,-0.05304544,0.00156144,-0.03117567,-0.02428413,-0.00516469,0.04334805,-0.05025227,0.01115535,0.00653657,-0.06820005,0.08107171,-0.0386109,-0.02828469,0.01454287,-0.0079913,0.03949336,0.00909844,0.01342179,0.05367392,0.0065934,0.00060429,-0.01740347,0.00222637,0.06420889,0.0237893,0.05775588,-0.04849499,-0.00107344,0.04410313,0.0209986,0.03701895,0.00294848,0.00237021,-0.03280351,0.04503869,-0.02764709,0.01960336,-0.0296387,-0.02966004,-0.00186665,0.02210681,0.02021352,-0.03445539,-0.0497972,0.07724287,0.034751,-0.07121933,0.0170518,-0.02335754,-0.06833211,0.07946403,0.05626013,-0.02618885,-0.07001042,0.03262287,0.02731412,0.02305617,0.03267282,-0.0150958,0.01605059,0.02680077,-0.04269066,-0.02565504,0.0505714,-0.04694183,0.04776006,0.00010954,0.01300066,-0.0310255,-0.03573065,-0.01479412,-0.01952061,0.0759709,0.04002402,-0.02528089,0.05578166,0.03731493,-0.02744376,-0.0530415,-0.03789011,-0.01328908,-0.00419457,0.01444722,-0.00334619,-0.00790273,-0.01044033,-0.07556505,0.03299956,0.04956583,-0.02615687,0.00336497,0.01902586,-0.00419108,0.00244179,-0.02043192,0.02360351,-0.05144284,0.00622916,-0.02153498,0.01204245,-0.0222407,-0.03871682,0.05845997,-0.00337135,0.01234271,-0.00987521,-0.02350215,-0.02687294,0.02460149,-0.02996044,0.049237,-0.02707771,-0.02893479,0.03497554,0.02487036,0.06655056,0.00830458,-0.07297733,0.00719742,-0.04824268,-0.05404864,0.0027964,0.05303033,-0.03190003,-0.04895295,0.03016304,0.04299011,-0.02046314,0.04141434,-0.06399111,-0.00611105,0.00660324,0.04198682,0.02517266,0.04216782,-0.04389661,-0.02768401,0.02085038,0.01596656,-0.03746427,-0.00348189,0.00815541,0.01977931,-0.01430978,-0.02190374,-0.07428235,-0.01369546,-0.04380452,-0.0510341,0.02777207,0.00019677,0.0345213,0.00982753,0.00309563,0.09628157,-0.06376193,-0.05871535,-0.02708526,-0.03641569,-0.01064709,0.01866479,0.04750587,-0.0058122,0.03059701,-0.03084754,0.02032667,-0.05901451,-0.04300315,-0.04151762,0.05705019,0.02380953,0.00042163,0.00445435,-0.02706242,-0.00427771,0.04762386,-0.07685938,0.03466672,-0.01971151,-0.04175472,0.00147852,0.06501259,0.01943166,-0.01024108,-0.07205812,0.02163864,-0.01357702,0.027623,-0.02726295,-0.05404032,-0.0360447,-0.00903958,-0.00298519,-0.09820704,0.03502942,-0.02240594,-0.04308262,-0.02493999,-0.01758598,0.02753619,0.01852008,-0.03878311,0.02312201,0.07686548,-0.07631537,0.00118088,-0.03583881,0.04278003,0.00931015,0.02184555,0.04033791,-0.10723826,-0.01864134,-0.03391735,-0.00017048,0.09453169,0.02504919,-0.00256367,-0.06550955,-0.0179005,0.00333866,0.01199942,0.01277969,0.00000925,-0.02148516,-0.01408569,-0.00420598,-0.01178896,0.04261124,0.00401767,-0.01175773,-0.0129763,0.01466762,0.00378505,-0.07461639,0.01562573,0.04191245,-0.0068448,-0.03140884,-0.07422313,0.00029402,0.04689661,0.01601649,-0.01455736,0.00660612,-0.00854287,0.03251353,-0.06016216,0.01656276,-0.02712159,0.00119447,-0.01720817,-0.01756748,0.04566131,0.00915267,-0.02513005,-0.04068712,0.0413785,-0.04038167,0.03679476,-0.03364753,0.03496313,0.00519204,0.0018463,0.04012099,-0.02033264,0.027967,0.02647443,0.06124959,-0.00515092,-0.02069371,-0.02526792,0.05542473,-0.02239886,-0.00243599,0.01625293,-0.02613548,0.0396773,0.03672137,0.01532275,-0.01200896,-0.05168452,0.01887766,-0.01541614,0.0111225,-0.00846473,0.06696844,-0.019382,0.00398289,0.05295318,0.03915114,-0.01550414,-0.01178828,0.00123666,0.02576664,0.04169213,-0.00245469,-0.02194197,-0.07553426,0.04314341,0.04056691,-0.0026751,-0.00679199,0.06027745,0.01702021,0.0226997,0.00694257,0.07018942,0.05699719,-0.03730426,-0.02302634,0.00255623,-0.02932625,-0.04447932,0.04718082,0.02594512,-0.00673538,0.03442056,-0.00861658,-0.05284887,-0.05606916,0.01133179,-0.00341013,0.05348209,0.00529741,-0.0350411,-0.00240513,-0.01148956,0.08004428,-0.05282649,-0.02759698,-0.01960997,0.03512291,-0.00430444,-0.04415531,0.01700818,0.01647929,0.00238839,0.05709422,0.01489037,-0.02749802,0.02898771,-0.00103874,0.02062648,-0.07448993,-0.00494505,0.03964277,-0.00149004,0.0467735,0.00789376,-0.0586745,-0.01558817,-0.02563454,-0.02050448,-0.0549034,0.04815334,-0.00827012,-0.02896372,-0.01061561,0.0139966,-0.02036975,0.00265638,-0.02994627,-0.04344169,0.03817555,0.06075223,-0.02895157,-0.00938524,0.00575732,0.09735353,-0.07899208,0.01047153,0.00944074,-0.01570994,-0.00362244,0.01077906,0.04037976,0.01664554,-0.03349318,-0.03357854,0.00874499,0.00105623,-0.03570039,-0.03709136,0.01550212,0.0609725,-0.02554816,0.0215459,0.03491795,0.04095932,-0.03598245,-0.00019827,-0.05269153,-0.01188027,-0.05995382,0.00378387,-0.03726373,-0.036466,0.06923015,-0.01148401,-0.04658653,-0.00889648,-0.04728291,-0.06284164,-0.00644779,-0.01366769,0.10812844,-0.0439352,-0.01868664,0.05665074,0.02066503,0.01819356,-0.00083179,-0.00771086,0.02723807,0.02447217,0.03908264,-0.04181975,-0.00639996,-0.0015168,0.03318381,0.02110614,0.00786939,-0.04231222,-0.00754492,-0.01701471,-0.00289274,0.04064399,-0.06468495,-0.04805555,0.02462005,-0.01340752,-0.06395883,-0.02998274,-0.01988328,-0.00230229,-0.00683652,0.03993436,0.01128248,0.03252966,-0.04984653,0.04123367,-0.01527444,-0.03056164,0.0086907,-0.04563516,-0.06657543,-0.0163629,-0.01969031,-0.04773822,0.00878642,-0.00152526,-0.0424404,0.02748892,0.02480436,-0.01157406,-0.01898509,0.03566631,-0.03749254,0.01179383,-0.05934035,-0.02727247,0.02289455,0.00454647,0.0379254,0.01569996,-0.0563629,0.03971169,-0.03264274,0.03556492,0.00405205,0.03137062,-0.0003175,0.01433596,0.01338911,0.04053841,-0.05935923,-0.01403778,-0.04825713,0.00894022,0.00854927,-0.00490745,-0.02611958,0.01990957,0.03999035,0.00260446,-0.0319097,-0.00952361,0.04998403,-0.00496225,0.05604581,0.05634373,0.00951326,0.01171185,0.00313528,0.04010411,0.05877768,-0.06950887,0.03164596,0.03188685,-0.03514823,0.02030332,-0.01726126,-0.00141989,0.05388435,-0.02658737,0.04456462,0.02827851,0.04653601,0.05030459,0.02219103,-0.06586975,-0.00131954,-0.0825161,-0.02060272,0.00607334,0.0327422,0.01976334,-0.03711495,-0.01124795,0.04501448,0.00478681,0.02735541,-0.00920989,-0.03582951,0.02634769,0.00660713,-0.01307086,-0.05821611,-0.02598229,-0.0204117,-0.03352132,-0.00225656,0.05197264,-0.0060649,-0.00344036,0.02840487,-0.03804894,-0.02332542,-0.06176978,0.04505974,0.04631764,-0.01510562,0.03864302,-0.05659347,7.7e-7,-0.02033107,0.02736218,-0.00159391,0.02409689,-0.01231623,-0.03598296,-0.00537588,0.00086849,0.01424399],"last_embed":{"hash":"z5kblb","tokens":680}}},"text":null,"length":0,"last_read":{"hash":"z5kblb","at":1749002757612},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl","lines":[23,110],"size":1526,"outlinks":[{"title":"windows系统","target":"windows系统","line":3},{"title":"Linux","target":"Linux","line":12},{"title":"windows系统","target":"windows系统","line":21},{"title":"Linux","target":"Linux","line":22},{"title":"Kali Linux","target":"Kali Linux","line":78}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#---frontmatter---","lines":[1,8],"size":89,"outlinks":[{"title":"基于WSL的隧道转发","target":"基于WSL的隧道转发","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介","lines":[9,22],"size":547,"outlinks":[{"title":"bash shell","target":"bash shell","line":3},{"title":"Linux","target":"Linux","line":3},{"title":"Linux","target":"Linux","line":3},{"title":"Ruby","target":"Ruby","line":3},{"title":"windows系统","target":"windows系统","line":3},{"title":"内核包","target":"https://learn.microsoft.com/zh-cn/windows/wsl/install-manual#step-4---download-the-linuxkernel-update-package ","line":5},{"title":"windows系统","target":"windows系统","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介#基于Windows的子系统(Linux系统)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介#基于Windows的子系统(Linux系统)","lines":[10,22],"size":541,"outlinks":[{"title":"bash shell","target":"bash shell","line":2},{"title":"Linux","target":"Linux","line":2},{"title":"Linux","target":"Linux","line":2},{"title":"Ruby","target":"Ruby","line":2},{"title":"windows系统","target":"windows系统","line":2},{"title":"内核包","target":"https://learn.microsoft.com/zh-cn/windows/wsl/install-manual#step-4---download-the-linuxkernel-update-package ","line":4},{"title":"windows系统","target":"windows系统","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介#基于Windows的子系统(Linux系统)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#简介#基于Windows的子系统(Linux系统)#{1}","lines":[11,22],"size":515,"outlinks":[{"title":"bash shell","target":"bash shell","line":1},{"title":"Linux","target":"Linux","line":1},{"title":"Linux","target":"Linux","line":1},{"title":"Ruby","target":"Ruby","line":1},{"title":"windows系统","target":"windows系统","line":1},{"title":"内核包","target":"https://learn.microsoft.com/zh-cn/windows/wsl/install-manual#step-4---download-the-linuxkernel-update-package ","line":3},{"title":"windows系统","target":"windows系统","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装WSL": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装WSL","lines":[24,26],"size":63,"outlinks":[{"title":"windows系统","target":"windows系统","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装WSL#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装WSL#{1}","lines":[25,26],"size":54,"outlinks":[{"title":"windows系统","target":"windows系统","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限","lines":[27,35],"size":229,"outlinks":[{"title":"Linux","target":"Linux","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{1}","lines":[28,30],"size":102,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{2}","lines":[31,32],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{3}","lines":[33,34],"size":67,"outlinks":[{"title":"Linux","target":"Linux","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启WSL的安全权限#{4}","lines":[35,35],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM","lines":[36,54],"size":485,"outlinks":[{"title":"windows系统","target":"windows系统","line":8},{"title":"Linux","target":"Linux","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{1}","lines":[37,41],"size":199,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{2}","lines":[42,44],"size":103,"outlinks":[{"title":"windows系统","target":"windows系统","line":2},{"title":"Linux","target":"Linux","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{3}","lines":[45,48],"size":88,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{4}","lines":[49,52],"size":59,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{5}","lines":[50,52],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{6}","lines":[53,53],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#开启DISM#{7}","lines":[54,54],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali","lines":[55,65],"size":113,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{1}","lines":[57,59],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{2}","lines":[60,65],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#安装Kali#{3}","lines":[61,65],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移","lines":[66,99],"size":496,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{1}","lines":[67,72],"size":152,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{2}","lines":[73,73],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{3}","lines":[74,77],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{4}","lines":[78,78],"size":19,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{5}","lines":[79,82],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{6}","lines":[83,83],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{7}","lines":[84,87],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{8}","lines":[88,88],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{9}","lines":[89,92],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{10}","lines":[93,93],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{11}","lines":[94,97],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{12}","lines":[95,97],"size":34,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{13}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{13}","lines":[98,98],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{14}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#子系统迁移#{14}","lines":[99,99],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面","lines":[100,110],"size":126,"outlinks":[{"title":"Kali Linux","target":"Kali Linux","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{1}","lines":[101,103],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{2}","lines":[104,107],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{3}","lines":[105,107],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{4}","lines":[108,109],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#安装wsl#使用[[Kali Linux]]的GUI界面#{5}","lines":[110,110],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置","lines":[111,128],"size":367,"outlinks":[{"title":"NAT","target":"NAT","line":2},{"title":"Pasted image 20240705132241.png","target":"Pasted image 20240705132241.png","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{1}","lines":[112,115],"size":111,"outlinks":[{"title":"NAT","target":"NAT","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{2}","lines":[116,119],"size":59,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{3}","lines":[117,119],"size":34,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{4}","lines":[120,120],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{5}","lines":[121,121],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{6}","lines":[122,122],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{7}","lines":[123,123],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{8}","lines":[124,124],"size":36,"outlinks":[{"title":"Pasted image 20240705132241.png","target":"Pasted image 20240705132241.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{9}","lines":[125,126],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL的网络配置#{10}","lines":[127,128],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动","lines":[129,143],"size":295,"outlinks":[{"title":"#修改WSL配置文件","target":"#修改WSL配置文件","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{1}","lines":[130,130],"size":16,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{2}","lines":[131,131],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{3}","lines":[132,136],"size":108,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{4}","lines":[133,136],"size":81,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{5}","lines":[137,137],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#开机自启动#{6}","lines":[138,143],"size":107,"outlinks":[{"title":"#修改WSL配置文件","target":"#修改WSL配置文件","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL配置文件": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md#修改WSL配置文件","lines":[144,147],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md": {"path":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09240332,0.00458976,-0.02247869,-0.05152578,0.00445898,-0.00697071,-0.0938212,0.0205155,0.03599713,-0.02667364,0.0014883,-0.03543838,0.03838866,0.03241917,0.04098187,0.0015188,-0.02672837,0.0264016,-0.05809403,-0.00514826,0.05298212,-0.06172568,-0.00632244,-0.06579359,-0.03152711,0.00440181,0.01505126,0.00306166,-0.02276057,-0.14192376,-0.01183697,-0.00603676,0.03496629,0.04766882,0.00551694,-0.03490269,0.05548946,0.02674743,-0.03076798,0.06527641,0.02275646,0.02705793,0.02823325,0.01620215,-0.01485765,-0.05940243,-0.03469278,-0.01625865,-0.0096408,-0.04176534,-0.04533107,-0.02299082,-0.06641849,0.00918382,-0.04836519,-0.01375836,0.01097739,0.03317147,0.07321395,0.00803538,0.03389953,0.00448597,-0.2243311,0.06253518,0.01372885,-0.00351891,-0.00653356,-0.02587863,0.01643365,0.0428003,-0.07203456,0.01870638,-0.00297685,0.05529483,0.055558,-0.04336405,-0.00464287,-0.03176956,-0.01065439,-0.01583358,-0.02538824,0.06093604,-0.00299086,-0.00403516,-0.06724742,0.01202441,-0.01152785,0.00486913,0.03039636,0.01602879,-0.02870509,-0.08377086,0.01278191,0.00080627,0.01411901,0.00375017,0.01450977,0.04060179,-0.12630855,0.10656565,-0.0761456,-0.03042069,0.02399844,-0.08146722,0.00370798,-0.00023885,-0.04910437,-0.01347953,-0.06561555,-0.00884881,-0.05608701,-0.0216244,0.04375289,-0.00749714,0.02471993,-0.00382345,0.0159867,-0.05056159,-0.01552702,0.01966506,-0.00224879,-0.01843433,0.10127432,-0.01955312,-0.0034691,-0.02309191,0.05691743,0.05577616,0.02289686,0.04982287,0.02885943,-0.05104003,-0.03131934,0.01937684,-0.00486769,0.03200188,-0.01494628,0.00801925,-0.00534504,-0.04400513,-0.01278988,-0.03815639,0.00781851,-0.09293886,-0.05617816,0.08890761,-0.03607368,0.01795015,0.04587108,-0.01787828,0.01933938,0.05310835,-0.0303974,-0.01660527,-0.02652096,0.00735306,0.10509258,0.14984198,-0.04176509,-0.01613785,-0.00722304,0.01110627,-0.08206873,0.16564219,0.02536777,-0.04303335,0.00488029,-0.00164353,0.05410931,-0.00749337,-0.00017723,0.01818693,0.00791247,0.03641925,0.08136434,-0.01863485,-0.04033136,0.00908048,0.06004705,-0.00178825,0.05601823,-0.00246019,-0.08055535,0.05338768,0.04217538,-0.08977581,-0.01805172,-0.05577399,-0.03354004,-0.0353545,-0.11482967,0.00234531,0.01224708,-0.01835058,0.00160403,-0.05257268,0.00333782,0.01179675,0.01868453,-0.05601712,0.08191808,0.01902705,-0.07564177,-0.05405911,-0.0374721,-0.04131735,0.03898632,-0.01815071,0.04285515,0.01976192,0.00880527,0.03177872,0.00884161,0.04222932,-0.03404275,0.00772762,-0.01651175,0.03426909,0.02031572,0.03950236,-0.00132247,-0.02186014,-0.10092136,-0.2261008,-0.04403357,0.05240598,-0.03385948,-0.01381085,0.01362177,0.05610215,-0.00356185,0.07408129,0.06565888,0.09579334,0.05337505,-0.04703755,-0.02214439,0.03271421,0.01375484,0.0154198,-0.00859263,-0.02218107,0.00446171,0.00041504,0.04040802,-0.00611796,-0.02346232,0.0292137,-0.04231209,0.09553088,0.02599053,0.04980443,0.02688281,0.01380708,0.0085657,0.02637839,-0.11414761,-0.01146156,0.08372638,-0.01623012,-0.03730303,0.00277814,-0.02334019,0.00838722,0.01355811,-0.04007968,-0.10590015,-0.0044751,-0.01403423,-0.00621866,-0.00814834,-0.00723119,0.0539225,-0.0059302,0.02198967,0.0086196,0.01190615,-0.01190378,-0.02673336,-0.02200586,-0.06756084,0.00133489,0.00446465,0.02269553,-0.04890326,0.03776081,0.02904492,0.01082146,-0.0128974,0.01772327,-0.02086653,0.00257404,-0.03341622,-0.05659755,0.12294399,0.01328162,-0.03845797,0.0263415,0.072578,-0.00568884,-0.0403224,0.01687637,-0.00575617,0.04602629,0.0088264,0.04864966,0.02784662,0.01540327,-0.00100142,0.00556475,-0.01608187,0.07553512,-0.05470462,-0.08677986,-0.01987741,-0.05050274,0.01301007,0.07828639,-0.04339487,-0.30095667,0.02589241,0.03250124,0.00205883,0.02356553,0.02200857,0.03416838,-0.00228363,-0.06517625,-0.01554988,-0.04223644,0.06114418,-0.02730159,-0.05394506,-0.02848856,-0.06908835,0.07140856,-0.00646604,0.09535725,0.00602506,-0.0047828,0.0543165,0.20094934,0.03206688,0.1018948,0.00324648,0.02943982,0.05795858,0.07847351,0.04691496,0.03513338,-0.04149945,0.00136334,-0.03508909,0.03147869,0.05454618,-0.03796773,0.02309535,0.05760122,0.01853246,-0.01733425,0.09007174,-0.05296475,0.01930355,0.09958115,0.00371509,0.02588625,-0.06472743,-0.00856536,0.05478005,0.04597998,0.03505578,-0.01109206,-0.03270254,0.01511024,0.01440096,0.0219664,-0.08153225,-0.06377564,0.00064436,0.02397629,-0.02984175,0.1075004,0.10199356,0.07875502],"last_embed":{"hash":"fca7f5c10e74d5bcffabfc8f26169f0c79c50080590514b80a148474696a54eb","tokens":490}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10496572,-0.02066137,0.05934949,0.04977274,-0.01987233,0.0772365,0.02215941,-0.02836599,-0.01377349,-0.02863212,0.01040756,0.03050469,0.01937152,-0.09539112,0.10332146,-0.00562773,-0.01761195,0.0106635,0.00248623,-0.05161548,0.00203152,-0.04152403,0.00079489,0.03220595,0.03694351,0.0236298,-0.03089771,-0.03222657,0.0241057,0.01368569,0.04911136,0.02926207,0.04099762,-0.00369493,0.06070412,0.04433398,0.01700189,0.00849038,0.0883826,0.00204878,0.01748373,0.01111981,-0.02472536,0.01746234,-0.04602792,0.02857146,-0.01435644,-0.00345997,0.03089339,-0.00639832,0.01388295,-0.02270246,0.0318776,-0.00263253,0.0353281,-0.01605034,-0.06491799,-0.05375083,-0.04622452,-0.02853777,-0.0170086,-0.04438076,0.01570659,-0.00752404,-0.00253529,-0.01143588,-0.02240001,-0.05528729,0.02167482,0.00972732,0.00559034,-0.0043482,0.06308943,-0.01492543,0.00633141,0.053282,0.01943132,-0.00720902,0.00262231,0.00725554,-0.05454193,0.00058952,0.02805646,0.06372616,0.03132394,-0.0351571,0.06325655,-0.076644,-0.0406444,0.01519199,-0.02522198,0.01550179,-0.08591011,-0.05370917,0.07965954,0.05172896,-0.00284226,-0.02530103,0.01201456,-0.00975082,-0.01419142,0.00344938,-0.06914306,0.01012991,-0.02772915,-0.03086641,-0.03215047,0.06433905,-0.01533077,-0.00724704,0.01777129,0.01339464,-0.05849536,0.00354619,-0.0422245,0.00522707,-0.01336731,-0.00228154,-0.01945768,0.0115089,0.02084218,0.06932221,-0.01820254,-0.01710739,-0.01945356,-0.06794498,0.02981875,0.00281133,0.06069339,-0.07179137,0.00014355,0.06186776,0.01353812,0.01809549,0.04800758,0.04285548,0.05416639,-0.02100816,-0.03256463,0.0030919,0.02453728,0.01692148,0.01870786,0.00530371,-0.02168719,0.02192122,-0.01982673,0.00640534,-0.00414886,0.01935392,0.03092002,0.00063593,-0.01716658,-0.03481648,-0.00200003,-0.02635106,0.07453582,-0.01539436,-0.00598687,-0.05275212,-0.02034599,0.02664305,-0.08170468,-0.0050437,0.00982642,-0.02924742,-0.05627217,-0.00134327,-0.00104929,0.01701034,0.03349678,0.08227248,0.03539107,-0.07724418,0.01588677,-0.02422678,-0.00415001,-0.04102933,-0.01508306,0.05087901,0.0595668,0.03892117,0.05628363,-0.02602232,-0.0286406,0.04358662,-0.0267318,-0.00301451,-0.03134239,0.01042758,0.02014549,0.05205454,0.03524471,0.00140912,0.02810519,-0.0002544,0.01260759,-0.06344862,0.02215654,0.04356837,-0.05715807,0.01529186,-0.03605839,-0.08947194,0.07293598,-0.00821353,0.01725285,-0.02430146,-0.01991128,0.01077427,-0.02962421,0.00368512,-0.00019342,-0.04448483,0.00988636,-0.01177122,0.00599442,-0.01301745,0.046706,-0.05817311,0.01823871,0.0035171,-0.0601762,0.06110733,-0.0283204,-0.02474942,-0.00579022,-0.01597843,0.03014684,-0.01079357,0.00075478,0.05532585,0.01735909,-0.00357538,-0.02475688,-0.01455557,0.05190744,-0.0077244,0.05651672,-0.04406324,-0.04415193,0.02884063,0.06375972,0.00165008,0.00689394,-0.00509753,-0.02863914,0.03754289,0.00189762,0.02549013,-0.02118803,0.00402867,0.00496682,0.02781753,0.0000818,-0.03454747,-0.04912271,0.0668181,0.07861135,-0.05227171,0.02845896,-0.02465617,-0.06651055,0.06523962,0.04072867,-0.02491705,-0.06519892,0.01533839,0.03584741,-0.00137832,0.03286041,-0.02532014,0.01090976,0.00854503,-0.04043061,-0.02493682,0.02137467,-0.02666681,0.05668025,-0.01839626,0.01481474,-0.04599717,-0.0394941,-0.02909032,-0.0156367,0.06952932,0.04003756,-0.03370382,0.0464458,0.04065173,-0.00943822,-0.02714481,-0.04716339,-0.01520898,0.00288433,-0.03532709,-0.01739459,-0.00309704,-0.00767216,-0.07329736,0.02124492,0.04605426,-0.0109522,-0.02177748,0.02645062,-0.00404018,0.00322007,0.00578207,0.01602746,-0.04686591,0.00339521,-0.02128115,0.00702348,-0.06946515,-0.03163793,0.06353776,0.00219353,0.00305932,-0.02593419,-0.01745653,-0.0184139,0.04308258,-0.03401415,0.02715341,-0.01505701,-0.01766489,-0.01843906,0.04749748,0.08379577,0.02302246,-0.05288389,-0.00586256,-0.04653071,-0.05652462,0.01519198,0.05111078,-0.0075772,-0.04084722,0.02657144,0.0483196,-0.06894895,0.01839331,-0.05238624,-0.01958613,-0.0132365,0.04641618,0.0214591,0.05570374,-0.05371046,-0.02895966,-0.00630205,0.03131196,-0.045243,-0.00394669,-0.02689949,0.04111996,-0.03013909,-0.01945041,-0.07858392,-0.00376202,-0.01652536,-0.03526252,0.04691121,-0.00621678,0.04545971,0.01615055,0.02953035,0.07650119,-0.04947619,-0.05132233,-0.02206886,-0.03621517,-0.00896038,0.08102067,0.01411777,-0.00693976,0.01908249,-0.03066295,0.04848253,-0.05423971,-0.04583855,-0.04697224,0.04969621,0.02263989,-0.02637007,-0.00877637,-0.00663877,-0.00376415,0.06578947,-0.0983872,0.02758868,-0.00359088,-0.04448331,-0.01109419,0.05228483,0.01571533,-0.03873157,-0.06097686,0.01015303,-0.03707138,0.02821215,-0.00931677,-0.0295437,-0.0239706,-0.02824238,0.00234667,-0.08757401,0.0501574,-0.01354085,-0.02361514,-0.00719777,-0.01930496,0.03703124,0.02422538,-0.05322707,0.01774735,0.08938392,-0.08795127,-0.00718056,-0.02480535,0.04026065,0.00531067,0.02394586,0.03573608,-0.10222045,0.01382626,-0.05169656,0.00444306,0.06222492,0.03081536,-0.01497886,-0.05900036,-0.02964646,-0.00088773,0.01555443,0.00872423,0.01546674,-0.03871937,0.00119597,-0.02221923,-0.02905355,0.02048803,-0.00255399,-0.01220372,-0.02633468,0.0053557,-0.01466391,-0.07035527,0.0094765,0.0269835,-0.00401224,-0.02113947,-0.04546228,0.01727422,0.04154503,0.02111888,0.01016714,0.00987483,-0.00109402,0.03531663,-0.05710334,0.00667117,-0.02188813,-0.0235876,-0.00515216,0.03265271,0.0776436,0.01761581,-0.05078962,-0.04128955,0.01481148,-0.04387242,0.0378763,-0.05083915,0.02501355,0.01803433,0.01978362,0.05643205,-0.03722803,0.02609421,0.01909118,0.01434485,-0.03457493,-0.05887034,-0.03277026,0.03530127,-0.02399984,0.01819987,0.0306702,-0.03895126,0.03742723,0.02729187,-0.00360099,0.01843842,-0.06353737,-0.00100399,-0.03486916,0.00630571,0.00029902,0.05028588,-0.00978404,-0.02317633,0.02619387,0.04000542,0.00216108,0.00137877,-0.0377407,0.01560531,0.04210385,-0.04037602,-0.0013878,-0.03357871,0.01597529,0.00959367,-0.02048156,-0.01730656,0.04245698,-0.00281185,0.01009955,0.01570402,0.06808917,0.04357297,-0.04251045,-0.03841501,0.01746732,-0.00313834,-0.05413682,0.03767386,0.0211715,-0.01443793,0.02701142,-0.0005443,-0.04183965,-0.05787672,0.0148967,-0.02148768,0.08001653,0.00322903,-0.03696674,0.00486865,-0.01474479,0.06334776,-0.04952228,-0.01854488,-0.02030732,0.04596801,0.00060601,-0.0127768,0.00575181,0.01610729,0.00116329,0.05966105,0.0193881,0.0004517,0.00515218,-0.00878646,0.0263166,-0.06281226,-0.02799941,0.04507707,-0.00775227,0.07519586,-0.00440132,-0.03642258,0.00973671,-0.04541795,-0.04815072,-0.06522816,0.05170797,0.0028567,-0.06176409,-0.00873966,0.03163698,-0.02301374,-0.00938204,-0.03508904,-0.03219802,0.03560122,0.05292143,-0.02421558,0.00607048,-0.02219515,0.09218638,-0.0926562,0.01094416,0.02425985,0.00494745,-0.01400219,0.01403085,0.00711077,0.0340094,-0.05365979,-0.0241142,-0.01239624,-0.01294909,-0.04323275,-0.04670287,0.03175749,0.05646465,-0.02552311,-0.00198672,0.03391137,0.02642089,-0.01029344,-0.00825435,-0.0358071,-0.0045465,-0.03749398,0.01155788,0.00905575,-0.02681921,0.09238694,0.00515607,-0.05441929,0.016498,-0.03147708,-0.02228137,0.00152142,0.00779269,0.07794633,-0.06175473,-0.05723092,0.01950557,0.01442254,0.01195859,0.0119742,0.00751002,0.02039993,-0.00263017,0.05093424,-0.03225402,-0.01263191,0.0174729,0.02586294,0.02957676,0.0160297,-0.06017242,-0.01384348,0.00581517,0.00003068,0.04158584,-0.04619075,-0.01400293,-0.00153715,-0.01323809,-0.05533936,-0.04442759,-0.00947414,-0.0222992,0.03193519,0.04774363,-0.01068205,0.04874894,-0.02811496,0.01511773,-0.03903564,-0.04141735,0.01881322,-0.02010044,-0.05807641,-0.00753771,-0.02109166,-0.05493296,0.03443756,0.00101596,-0.05585075,0.0188882,0.0277203,-0.05147395,-0.03581873,0.02097586,-0.02004953,0.01320087,-0.05807294,-0.00025835,0.03109771,-0.01092883,0.03716201,0.00810985,-0.05433587,0.02198845,-0.03188574,0.04000724,-0.00678858,0.0299458,-0.01132746,0.00363811,-0.00649973,0.02765404,-0.0319679,-0.0281633,-0.01836527,0.00045743,-0.00890408,-0.01953198,-0.05734326,0.0159691,0.03980236,-0.0047079,-0.04212402,-0.01297096,0.05659859,-0.02210218,0.08183309,0.02524741,0.01372593,0.00869331,-0.01421521,0.04080908,0.06150616,-0.04156467,0.01742033,0.0194656,-0.04207899,0.03439359,0.01780761,-0.00215771,0.02216254,-0.05191192,0.05156195,0.0261075,0.05149192,0.03276099,0.02086522,-0.04009282,-0.00925324,-0.07952648,-0.01494034,0.00736644,0.04138276,0.01142258,-0.03687688,-0.03737206,0.03795716,-0.00854348,0.00994324,0.00772284,-0.01732305,0.0222351,-0.00573762,0.02168067,-0.05746521,-0.02029759,-0.04312952,-0.02143747,0.02635533,0.01902317,-0.00117858,-0.00270765,0.04950377,-0.03518573,0.01324699,-0.04133532,0.0788446,0.05948631,-0.00087292,0.04006381,-0.05473921,8.8e-7,-0.0167363,-0.02001043,0.00776506,0.00620368,-0.0329702,-0.03560871,0.0139212,0.03562467,0.02816838],"last_embed":{"tokens":1235,"hash":"qk22tt"}}},"last_read":{"hash":"qk22tt","at":1750820107030},"class_name":"SmartSource","outlinks":[{"title":"基于WSL的隧道转发","target":"基于WSL的隧道转发","line":5},{"title":"bash shell","target":"bash shell","line":11},{"title":"Linux","target":"Linux","line":11},{"title":"Linux","target":"Linux","line":11},{"title":"Ruby","target":"Ruby","line":11},{"title":"windows系统","target":"windows系统","line":11},{"title":"内核包","target":"https://learn.microsoft.com/zh-cn/windows/wsl/install-manual#step-4---download-the-linuxkernel-update-package ","line":13},{"title":"windows系统","target":"windows系统","line":18},{"title":"windows系统","target":"windows系统","line":25},{"title":"Linux","target":"Linux","line":34},{"title":"windows系统","target":"windows系统","line":43},{"title":"Linux","target":"Linux","line":44},{"title":"Kali Linux","target":"Kali Linux","line":100},{"title":"NAT","target":"NAT","line":112},{"title":"Pasted image 20240705132241.png","target":"Pasted image 20240705132241.png","line":124},{"title":"#修改WSL配置文件","target":"#修改WSL配置文件","line":140}],"metadata":{"tags":["操作系统/windows/子系统"],"进阶知识":["[[基于WSL的隧道转发]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,22],"#简介#基于Windows的子系统(Linux系统)":[10,22],"#简介#基于Windows的子系统(Linux系统)#{1}":[11,22],"#安装wsl":[23,110],"#安装wsl#安装WSL":[24,26],"#安装wsl#安装WSL#{1}":[25,26],"#安装wsl#开启WSL的安全权限":[27,35],"#安装wsl#开启WSL的安全权限#{1}":[28,30],"#安装wsl#开启WSL的安全权限#{2}":[31,32],"#安装wsl#开启WSL的安全权限#{3}":[33,34],"#安装wsl#开启WSL的安全权限#{4}":[35,35],"#安装wsl#开启DISM":[36,54],"#安装wsl#开启DISM#{1}":[37,41],"#安装wsl#开启DISM#{2}":[42,44],"#安装wsl#开启DISM#{3}":[45,48],"#安装wsl#开启DISM#{4}":[49,52],"#安装wsl#开启DISM#{5}":[50,52],"#安装wsl#开启DISM#{6}":[53,53],"#安装wsl#开启DISM#{7}":[54,54],"#安装wsl#安装Kali":[55,65],"#安装wsl#安装Kali#{1}":[57,59],"#安装wsl#安装Kali#{2}":[60,65],"#安装wsl#安装Kali#{3}":[61,65],"#安装wsl#子系统迁移":[66,99],"#安装wsl#子系统迁移#{1}":[67,72],"#安装wsl#子系统迁移#{2}":[73,73],"#安装wsl#子系统迁移#{3}":[74,77],"#安装wsl#子系统迁移#{4}":[78,78],"#安装wsl#子系统迁移#{5}":[79,82],"#安装wsl#子系统迁移#{6}":[83,83],"#安装wsl#子系统迁移#{7}":[84,87],"#安装wsl#子系统迁移#{8}":[88,88],"#安装wsl#子系统迁移#{9}":[89,92],"#安装wsl#子系统迁移#{10}":[93,93],"#安装wsl#子系统迁移#{11}":[94,97],"#安装wsl#子系统迁移#{12}":[95,97],"#安装wsl#子系统迁移#{13}":[98,98],"#安装wsl#子系统迁移#{14}":[99,99],"#安装wsl#使用[[Kali Linux]]的GUI界面":[100,110],"#安装wsl#使用[[Kali Linux]]的GUI界面#{1}":[101,103],"#安装wsl#使用[[Kali Linux]]的GUI界面#{2}":[104,107],"#安装wsl#使用[[Kali Linux]]的GUI界面#{3}":[105,107],"#安装wsl#使用[[Kali Linux]]的GUI界面#{4}":[108,109],"#安装wsl#使用[[Kali Linux]]的GUI界面#{5}":[110,110],"#修改WSL的网络配置":[111,128],"#修改WSL的网络配置#{1}":[112,115],"#修改WSL的网络配置#{2}":[116,119],"#修改WSL的网络配置#{3}":[117,119],"#修改WSL的网络配置#{4}":[120,120],"#修改WSL的网络配置#{5}":[121,121],"#修改WSL的网络配置#{6}":[122,122],"#修改WSL的网络配置#{7}":[123,123],"#修改WSL的网络配置#{8}":[124,124],"#修改WSL的网络配置#{9}":[125,126],"#修改WSL的网络配置#{10}":[127,128],"#开机自启动":[129,143],"#开机自启动#{1}":[130,130],"#开机自启动#{2}":[131,131],"#开机自启动#{3}":[132,136],"#开机自启动#{4}":[133,136],"#开机自启动#{5}":[137,137],"#开机自启动#{6}":[138,143],"#修改WSL配置文件":[144,147]},"last_import":{"mtime":1740737681858,"size":4538,"at":1748488128956,"hash":"qk22tt"},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/子系统/wsl.md","last_embed":{"hash":"qk22tt","at":1750820107030}},