{"mapMarkers": [{"id": "P001 ", "lastAccessed": 1750834883921, "markers": [{"id": "ID_3afb7a487b9b", "type": "default", "loc": [34.161818161230386, 156.79246379332153], "layer": "real", "mutable": true, "command": false, "percent": null, "description": null, "minZoom": null, "maxZoom": null, "tooltip": "hover"}], "overlays": [], "shapes": [], "files": ["", "地理数据/基础设施/甘肃省.md", "地理数据/基础设施/中国省份/河北省.md"]}], "defaultMarker": {"type": "default", "iconName": "map-marker", "color": "#dddddd", "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false}, "markerIcons": [{"type": "景区", "iconName": "street-view", "color": "#80ff9f", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": ["#成都/景区", "#大邑县/景区"], "minZoom": null, "maxZoom": null}, {"type": "演出中心", "iconName": "music", "color": "#f36de1", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": ["#成都/文化中心"], "minZoom": null, "maxZoom": null}, {"type": "飞机场", "iconName": "plane", "color": "#35c4f3", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": ["#成都/机场"], "minZoom": null, "maxZoom": null}, {"type": "城市", "iconName": "city", "color": "#2d97fb", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": ["中国/超大城市"], "minZoom": null, "maxZoom": null}, {"type": "住宅", "iconName": "home", "color": "#36dd6e", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}, {"type": "美食", "iconName": "underline", "color": "#ad58d5", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}, {"type": "剧院", "iconName": "theater-masks", "color": "#bc24b0", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}, {"type": "健身", "iconName": "dumbbell", "color": "#df2a2a", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}, {"type": "酒吧", "iconName": "beer", "color": "#9b3671", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}, {"type": "学院", "iconName": "school", "color": "#167ae3", "alpha": 1, "layer": true, "transform": {"size": 6, "x": 0, "y": -2}, "isImage": false, "tags": [], "minZoom": null, "maxZoom": null}], "color": "#dddddd", "lat": 39.983334, "long": -82.98333, "notePreview": false, "layerMarkers": true, "previousVersion": "6.0.5", "version": {"major": 6, "minor": 0, "patch": 5}, "warnedAboutMapMarker": false, "copyOnClick": true, "displayMarkerTooltips": "hover", "displayOverlayTooltips": true, "configDirectory": null, "mapViewEnabled": true, "mapViewParameters": {"darkMode": "false", "defaultZoom": "10 ", "distanceMultiplier": 1, "drawColor": "#3388ff", "geojson": ["[[成都市.json]]"], "geojsonColor": "#3388ff", "gpx": [], "gpxColor": "#3388ff", "height": "600px ", "image": "real", "imageOverlay": [], "isMapView": false, "layers": ["real"], "linksFrom": [], "linksTo": [], "marker": [], "commandMarker": [], "markerFile": [], "markerFolder": [], "markerTag": [["#成都/文化中心  "]], "maxZoom": 18, "minZoom": 1, "osmLayer": "false ", "overlay": [], "overlayColor": "blue", "scale": "1 ", "showAllMarkers": false, "tileServer": "https", "verbose": false, "zoomDelta": 1, "zoomFeatures": false, "id": "P001 ", "tileSubdomains": "1,2,3,4", "lat": "30.64 ", "long": "104.072 ", "width": "100% ", "maxzoom": "18 ", "minzoom": "1 ", "zoom": "13", "unit": "meters ", "# markerFolder": "Bar", "#darkMode": "true", "filterTag": []}, "enableDraw": true, "defaultUnitType": "imperial", "defaultTile": "https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png", "defaultTileDark": "https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}", "defaultAttribution": "&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors &copy; <a href=\"https://carto.com/attributions\">CARTO</a>", "defaultTileSubdomains": "1,2,3"}