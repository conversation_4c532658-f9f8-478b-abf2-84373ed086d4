{"interactiveSettings": {"tag": {"colormap": "winter", "colors": [], "unselected": [], "noneType": "none", "showOnGraph": true, "enableByDefault": true}, "link": {"colormap": "rainbow", "colors": [], "unselected": [], "noneType": "none", "showOnGraph": true, "enableByDefault": true}, "folder": {"colormap": "winter", "colors": [], "unselected": [], "noneType": ".", "showOnGraph": true, "enableByDefault": false}}, "additionalProperties": {}, "backupGraphOptions": {"collapse-filter": false, "showTags": false, "showAttachments": false, "hideUnresolved": false, "showOrphans": true, "collapse-color-groups": false, "colorGroups": [], "collapse-display": false, "showArrow": false, "textFadeMultiplier": 0, "nodeSizeMultiplier": 1, "lineSizeMultiplier": 1, "collapse-forces": false, "centerStrength": 0.518713248970312, "repelStrength": 10, "linkStrength": 1, "linkDistance": 250, "scale": 0.04862505672653992, "close": true}, "states": [{"id": "default-vault", "name": "Vault (default)", "engineOptions": {"colorGroups": [], "hideUnresolved": false, "showAttachments": false, "showTags": false, "localBacklinks": true, "localForelinks": true, "localInterlinks": false, "localJumps": 1, "lineSizeMultiplier": 1, "nodeSizeMultiplier": 1, "showArrow": false, "textFadeMultiplier": 0, "centerStrength": 0.518713248970312, "linkDistance": 250, "linkStrength": 1, "repelStrength": 10}, "toggleTypes": {"tag": [], "link": [], "folder": []}, "pinNodes": {}, "hiddenLegendRows": [], "collapsedLegendRows": []}, {"id": "b8ed4618-13a3-4987-8b5e-b3cd0af68ffa", "name": "test", "toggleTypes": {"link": [], "folder": [], "tag": []}, "pinNodes": {}, "engineOptions": {"colorGroups": [], "hideUnresolved": true, "showAttachments": false, "showOrphans": true, "showTags": false, "lineSizeMultiplier": 1, "nodeSizeMultiplier": 1, "showArrow": true, "textFadeMultiplier": 0, "centerStrength": 0.518713248970312, "linkDistance": 250, "linkStrength": 1, "repelStrength": 10}}], "startingStateID": "default-vault", "borderFactor": 0, "allowExternalImages": true, "allowExternalLocalImages": true, "nodesSizeFunction": "default", "nodesColorColormap": "YlOrRd", "nodesColorFunction": "default", "linksSizeFunction": "default", "linksColorColormap": "YlOrRd", "linksColorFunction": "default", "zoomFactor": 2, "maxNodes": 300, "delay": 500, "enableFeatures": {"graph": {"auto-enabled": true, "tags": false, "properties": false, "property-key": true, "links": false, "folders": false, "imagesFromProperty": true, "imagesFromEmbeds": false, "imagesForAttachments": true, "focus": true, "shapes": false, "elements-stats": false, "names": false, "icons": false, "arrows": false, "linksSameColorAsNode": false}, "localgraph": {"auto-enabled": true, "tags": true, "properties": true, "property-key": true, "links": true, "folders": true, "imagesFromProperty": true, "imagesFromEmbeds": false, "imagesForAttachments": true, "focus": false, "shapes": true, "elements-stats": true, "names": true, "icons": true, "arrows": true, "linksSameColorAsNode": true}}, "shapeQueries": {"circle": {"combinationLogic": "AND", "index": 0, "rules": []}, "square": {"combinationLogic": "AND", "index": 1, "rules": []}, "triangle": {"combinationLogic": "AND", "index": 2, "rules": []}, "diamond": {"combinationLogic": "AND", "index": 3, "rules": []}, "pentagon": {"combinationLogic": "AND", "index": 4, "rules": []}, "hexagon": {"combinationLogic": "AND", "index": 5, "rules": []}, "octagon": {"combinationLogic": "AND", "index": 6, "rules": []}, "decagon": {"combinationLogic": "AND", "index": 7, "rules": []}, "star4": {"combinationLogic": "AND", "index": 8, "rules": []}, "star5": {"combinationLogic": "AND", "index": 9, "rules": []}, "star6": {"combinationLogic": "AND", "index": 10, "rules": []}, "star8": {"combinationLogic": "AND", "index": 11, "rules": []}, "star10": {"combinationLogic": "AND", "index": 12, "rules": []}}, "multipleNodesData": {}, "exportSVGOptions": {"asImage": true, "onlyVisibleArea": false, "showNodeNames": true, "useCurvedLinks": false, "useNodesShapes": false, "showArcs": false, "showFolders": true, "useModifiedArrows": true, "useModifiedNames": true, "showIcons": false}, "fadeOnDisable": false, "focusScaleFactor": 1.8, "borderUnresolved": "", "invertArrows": false, "numberOfCharacters": null, "showOnlyFileName": false, "noExtension": false, "usePluginForIcon": true, "usePluginForIconColor": true, "useParentIcon": false, "collapseState": false, "collapseLegend": false, "flatArrows": false, "addBackgroundToName": false, "nameVerticalOffset": 0, "curvedLinks": true, "disableSource": false, "disableTarget": false, "invertNodeStats": false, "excludedSourcesFolder": [], "excludedTargetsFolder": [], "outlineLinks": false, "opaqueArrowsButKeepFading": false, "alwaysOpaqueArrows": false, "arrowScale": 1, "arrowFixedSize": false, "arrowColorBool": false, "arrowColor": "", "dynamicVerticalOffset": false, "resetAfterChanges": false, "collapsedSettings": {"automation": true, "tags": true, "properties": true, "links": true, "arrows": true, "folders": true, "images": true, "icons": true, "focus": true, "shapes": true, "elements-stats": true, "zoom": true, "names": true, "performances": false, "display": true, "beta": true}, "openInNewTab": false, "allowMultipleLinkTypes": false, "iconProperties": [""], "nodesSizeProperties": [""], "imageProperties": ["图片"], "usePropertiesForName": [], "revertAction": false, "enableCSS": false, "cssSnippetFilename": "", "spreadArcs": false, "weightArcs": false, "animateDotsOnLinks": false, "animationSpeedForDots": 1, "folderRadius": 50, "folderShowFullPath": true, "showNamesWhenNeighborHighlighted": false, "horizontalLegend": false, "useRadialMenu": false}