.ge-grid-view-container {
    padding: 0px;
    height: 100%;
}

/* 選擇資料夾的樣式 */
.ge-grid-view-folder-option {
    cursor: pointer;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
}

.ge-grid-view-folder-option:hover {
    background-color: var(--background-modifier-hover);
}

/* ascii tree prefix */
.ge-folder-tree-prefix {
    font-family: monospace;
    white-space: pre;
    color: var(--text-muted);
}

/* Grid 樣式 */
.ge-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--grid-item-width, 300px), 1fr));
    gap: 12px;
    padding: 12px !important;
    align-items: start;
    align-content: start;
    background: var(--background-secondary) !important;
    flex: 1;
    overflow-y: auto;
}

.is-mobile .ge-grid-container::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
}

/* Grid 項目樣式 */
.ge-grid-item {
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: var(--button-radius);
    padding: 10px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    gap: 14px;
    height: var(--grid-item-height);
}

.ge-grid-item:hover {
    transform: translateY(-2px);
    background-color: var(--text-selection) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ge-content-area {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.ge-break {
    grid-column: 1 / -1;
    height: 0;
    margin: 0;
    padding: 0;
    border-top: 1px solid var(--background-modifier-border);
    border-bottom: 1px solid var(--background-primary);
}

.ge-title-container {
    display: flex;
    align-items: center;
    gap: 0px;
    width: 100%;
}

.ge-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    color: var(--text-muted);
}

.ge-icon-container.ge-img {
    color: var(--color-blue);
}

.ge-icon-container.ge-video {
    color: var(--color-red);
}

.ge-icon-container.ge-audio {
    color: var(--color-purple);
}

.ge-icon-container.ge-pdf {
    color: var(--color-orange);
}

.ge-icon-container.ge-canvas {
    color: var(--color-green);
}

.ge-icon-container.ge-base {
    color: var(--color-cyan);
}

.ge-title {
    margin: 0 0 0 3px;
    font-size: var(--title-font-size);
    color: var(--text-normal);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    width: 100%;
}

.ge-note-button {
    color: var(--text-muted);
    cursor: pointer;
    margin-left: 8px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    background: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
}

.ge-note-button:hover {
    opacity: 1;
    color: var(--text-accent);
}

.ge-note-button svg {
    width: 18px;
    height: 18px;
}

.ge-content-area p {
    margin: 5px 0 0 0;
    color: var(--text-faint);
    font-size: 0.85em;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    height: 100%;
}

.ge-grid-item:hover p {
    color: var(--text-muted);
}

.ge-content-area p:empty {
    margin: 0;
}

.ge-image-area {
    width: var(--image-area-width);
    height: var(--image-area-height);
    flex-shrink: 0;
}

.ge-image-area img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.ge-image-area video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

/* 標籤樣式 */
.ge-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
    margin-bottom: 2px;
}

.ge-tag {
    display: inline-flex;
    align-items: center;
    padding: 0 6px;
    font-size: 0.8em;
    border: 1px solid var(--background-modifier-border);
    border-radius: 5px;
    background-color: var(--background-secondary-alt);
    color: var(--text-accent);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 資料夾項目的特殊樣式 */
.ge-grid-item.ge-folder-item {
    background-color: var(--background-primary-alt);
    border: 2px solid var(--background-modifier-border);
    padding: 7px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    height: 100%;
}

.ge-grid-item.ge-folder-item:hover {
    background-color: var(--background-modifier-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ge-grid-item.ge-folder-item .ge-icon-container {
    color: var(--interactive-accent);
}

.ge-grid-item.ge-folder-item .ge-title {
    color: var(--text-normal);
}

/* 調整資料夾項目的高度 */
.ge-grid-item.ge-folder-item .ge-content-area {
    min-height: 0px;
    justify-content: center;
}

.ge-grid-item.ge-folder-item .ge-title-container {
    align-items: center;
}

/* 日期分隔器樣式 */
.ge-date-divider {
    width: 100%;
    text-align: left;
    color: var(--text-normal);
    margin-left: 8px;
    grid-column: 1 / -1;
    pointer-events: none;
    user-select: none;
    margin-top: -1px;
    margin-bottom: -2px;
}

/* Pin分隔器樣式 */
.ge-pin-divider {
    width: 100%;
    text-align: left;
    color: var(--text-normal);
    margin-left: 8px;
    grid-column: 1 / -1;
    pointer-events: none;
    user-select: none;
    margin-top: -1px;
    margin-bottom: -2px;
}

/* 頂部按鈕區域樣式 */
.ge-header-buttons {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    background: var(--background-primary);
    border-bottom: 1px solid var(--background-modifier-border);
    flex-shrink: 0;
    justify-content: center;
}

.ge-header-buttons button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background-color: var(--interactive-normal);
    border-radius: var(--button-radius);
    color: var(--text-normal);
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    min-width: 38px;
}

.is-tablet .ge-header-buttons button:not(.clickable-icon) {
    padding: 6px 12px;
}

.ge-header-buttons button:hover {
    background-color: var(--interactive-hover);
    transform: translateY(-1px);
}

.ge-header-buttons button:active {
    transform: translateY(0);
}

/* 特定按鈕樣式 */
.ge-header-buttons .sort-button,
.ge-header-buttons .reselect-button,
.ge-header-buttons .refresh-button,
.ge-header-buttons .up-button {
    display: inline-flex;
    align-items: center;
}

.ge-foldername-content {
    justify-content: center;
    padding: 10px 8px 9px 8px;
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--background-modifier-border);
    font-size: var(--font-ui-medium);
}

.ge-foldername-content span {
    display: inline-flex;
    align-items: center;
    
}

.ge-foldername-content span.ge-mode-title {
    padding-left: 5px;
    padding-right: 3px;
}

/* 為資料夾名稱、分隔符等元素添加間距 */
.ge-foldername-content > *:nth-child(1) {
    margin-left: 11px;
    margin-right: 3px;
}

.ge-foldername-content > *:nth-child(2) {
    margin-right: 6px;
}

.ge-parent-folder-link {
    text-decoration: none;
    padding: 5px;
}

.ge-parent-folder-link:hover {
    border-radius: 4px;
    background-color: var(--background-modifier-hover);
    text-decoration: none;
}

.ge-parent-folder-link.ge-dragover {
    border: 2px dashed var(--interactive-accent);
    transform: scale(1.05);
}

/* 搜尋對話框樣式 */
.ge-search-container {
    margin-bottom: 8px;
    position: relative;
    display: flex;
    align-items: center;
}

.ge-search-input-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.ge-input-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
}

.ge-search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    color: var(--text-muted);
    cursor: pointer;
    z-index: 10;
}

.ge-search-clear-button:hover {
    color: var(--text-accent);
}

.ge-search-clear-button svg {
    width: 16px;
    height: 16px;
}

/* 標籤顯示區域 */
.ge-search-tag-display-area {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 5px;
    margin-bottom: 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    align-items: center;
}

.ge-search-tag-button {
    display: inline-flex;
    align-items: center;
    padding: 3px 7px;
    background-color: var(--background-modifier-active-hover);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding-right: 24px;
    height: 24px;
    border: none;
}

.ge-search-tag-delete-button {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s;
    opacity: 0.8;
}

.ge-search-tag-delete-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

.ge-search-tag-delete-button svg {
    width: 22px;
    height: 22px;
}

/* 標籤自動完成下拉選單 */
.ge-search-tag-suggestions {
    position: relative;
    margin-top: 4px;
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    max-height: 150px;
    overflow-y: auto;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.ge-search-tag-suggestion-item {
    padding: 4px 8px;
    cursor: pointer;
    white-space: nowrap;
}

.ge-search-tag-suggestion-item:hover,
.ge-search-tag-suggestion-item.is-selected {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent, white);
}

.ge-search-container input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
    padding-right: 25px;
}

.ge-search-container input:focus {
    border-color: var(--interactive-accent);
}

.ge-search-container input::placeholder {
    color: var(--text-muted);
}

/* 搜尋選項容器 */
.ge-search-options-container {
    display: flex;
    gap: 16px;
    margin: 4px;
}

/* 搜尋範圍設定樣式 */
.ge-search-scope-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px;
}

.ge-search-scope-checkbox {
    cursor: pointer;
}

.ge-search-scope-label {
    cursor: pointer;
    user-select: none;
    color: var(--text-normal);
}

/* 搜尋媒體檔案設定樣式 */
.ge-search-media-files-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px;
}

.ge-search-media-files-checkbox {
    cursor: pointer;
}

.ge-search-media-files-label {
    cursor: pointer;
    user-select: none;
    color: var(--text-normal);
}

.ge-button-container {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.ge-button-container button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.ge-button-container button:first-child {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.ge-button-container button:last-child {
    background-color: var(--interactive-normal);
    color: var(--text-normal);
}

.ge-button-container button:hover {
    transform: translateY(-1px);
}

.ge-button-container button:first-child:hover {
    background-color: var(--interactive-accent-hover);
}

.ge-button-container button:last-child:hover {
    background-color: var(--interactive-hover);
}

.ge-button-container button:active {
    transform: translateY(0);
}

/* 搜尋按鈕容器 */
.ge-search-button-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--background-primary);
    border-radius: 4px;
    padding: 0;
}

/* 搜尋按鈕啟用狀態 */
.ge-search-button-container button.active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

/* 搜尋文字容器 */
.ge-search-text-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: var(--background-modifier-active-hover);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    transition: border-color 0.15s ease;
}

.ge-search-text-container:hover {
    border-color: var(--interactive-accent);
}

/* 搜尋文字 */
.ge-search-text {
    font-size: 14px;
    color: var(--text-normal);
    flex-grow: 1; /* 讓文字填滿剩餘空間 */
    min-width: 0; /* 防止内容溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
}

/* 取消按鈕 */
.ge-clear-button {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    color: var(--text-muted);
    font-size: 14px;
    margin-right:4px; /* 添加右边距 */
    flex-shrink: 0; /* 防止被压缩*/
}

.ge-clear-button:hover {
    color: #FFF;
    background-color: var(--background-modifier-error-hover);
}

.ge-clear-button svg {
    width: 18px;
    height: 18px;
}

.ge-search-clear-button {
    color: var(--text-muted);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    position: absolute;
    right: 5px;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    padding: 3px;
}

.ge-search-clear-button:hover {
    color: var(--text-normal);
    background-color: var(--background-modifier-hover);
}

.ge-loading-indicator {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-size: 1.2em;
    color: var(--text-muted);
}

.ge-no-files {
    grid-column: 1 / -1;
    text-align: center;
    padding: 2em;
    color: var(--text-muted);
    font-size: 1.2em;
}

/* 資料夾搜尋輸入框樣式 */
.ge-folder-search-container {
    margin-bottom: 16px;
    padding: 0 4px;
}

.ge-folder-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    font-size: var(--font-ui-small);
}

.ge-folder-search-input:focus {
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px rgba(var(--interactive-accent-rgb), 0.2);
    outline: none;
}

.ge-folder-options-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 4px;
}

/* 鍵盤導航選中項的樣式 */
.ge-selected-option {
    background-color: var(--background-modifier-hover);
    border-left: 3px solid var(--interactive-accent) !important;
    padding-left: 5px !important;
}

/* 忽略資料夾設定 */
.ge-ignored-folders-container {
    margin-bottom: 16px;
}

.ge-ignored-folders-list {
    list-style: none;
    padding: 0;
}

.ge-ignored-folder-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 4px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-primary);
}

.ge-ignored-folder-path {
    flex-grow: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ge-ignored-folder-remove {
    background-color: var(--interactive-normal);
    color: var(--text-normal);
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.ge-ignored-folder-remove:hover {
    background-color: var(--interactive-hover);
}

/* 忽略資料夾樣式 */
.ignored-folder-patterns-container {
    margin-bottom: 16px;
}

.ge-ignored-folder-patterns-list {
    list-style: none;
    padding: 0;
}

/* 影片縮圖樣式 */
.ge-video-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-secondary);
    border-radius: 4px;
}

.ge-video-thumbnail svg {
    width: 40px;
    height: 40px;
    color: var(--text-accent);
}

/* 媒體檔案全螢幕顯示樣式 */
.ge-media-fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ge-media-view {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ge-media-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.ge-fullscreen-image {
    max-width: 100vw;
    max-height: 100vh;
    object-fit: contain;
}

.ge-fullscreen-video {
    max-width: 100vw;
    max-height: 100vh;
}

.ge-media-container video {
    margin: 0 auto;
}

.ge-fullscreen-file-name {
    position: fixed;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1007;
    white-space: nowrap;
}

.ge-media-close-button {
    position: fixed;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1010;
}

.ge-media-close-button:hover {
    background-color: rgba(255, 0, 0, 0.7);
}

/* 左右切換區域 */
.ge-media-prev-area,
.ge-media-next-area {
    position: absolute;
    top: 15%;
    height: 70%;
    width: 10%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1005;
    cursor: pointer;
}

.ge-media-prev-area {
    left: 0;
}

.ge-media-next-area {
    right: 0;
}

/* MediaModal 樣式 */
.ge-media-modal {
    padding: 0 !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
}

.ge-media-modal .modal-header {
    margin: 0 !important;
}

.ge-media-modal .modal-close-button {
    display: none !important;
}

.ge-media-modal-content {
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%;
}

/* 媒體 Modal 樣式 */
.ge-media-modal {
    padding: 0 !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    max-width: 100% !important;
    max-height: 100% !important;
}

/* 選中項目的樣式 */
.ge-selected-item {
    outline: 1px solid var(--vault-profile-color-hover) !important;
    outline-offset: -1px;
    position: relative;
    z-index: 1;
}

/* 拖曳相關樣式 */
.ge-grid-item.ge-dragging {
    opacity: 0.5;
    transform: scale(0.95);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.ge-folder-item.ge-dragover {
    border: 2px dashed var(--interactive-accent);
    transform: scale(1.05);
}

/* 音樂播放器 */
.ge-floating-audio-player {
    position: fixed;
    width: 300px;
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1100;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: box-shadow 0.3s ease;
}

.ge-floating-audio-player:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.ge-audio-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-normal);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 24px;
    cursor: default;
    margin: 2px; 
}

.ge-floating-audio-player audio {
    width: 100%;
    outline: none;
}

.ge-audio-close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
    z-index: 1102; 
}

.ge-audio-close-button:hover {
    background-color: var(--background-modifier-error-hover);
    color: white;
}

.ge-audio-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    cursor: move;
    border-radius: 8px 8px 0 0;
    z-index: 1101; 
}

.ge-audio-dragging {
    opacity: 0.9;
    user-select: none;
}

/* 設定描述區域樣式 */
.ge-setting-desc {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ge-setting-number-input {
    width: 60px;
}

